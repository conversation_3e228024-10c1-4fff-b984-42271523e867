# Task ID: 40
# Title: Implement 'plan' Command for Task Implementation Planning
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Create a new 'plan' command that appends a structured implementation plan to tasks or subtasks, generating step-by-step instructions for execution based on the task content.
# Details:
Implement a new 'plan' command that will append a structured implementation plan to existing tasks or subtasks. The implementation should:

1. Accept an '--id' parameter that can reference either a task or subtask ID
2. Determine whether the ID refers to a task or subtask and retrieve the appropriate content from tasks.json and/or individual task files
3. Generate a step-by-step implementation plan using AI (Claude by default)
4. Support a '--research' flag to use Perplexity instead of Claude when needed
5. Format the generated plan within XML tags like `<implementation_plan as of timestamp>...</implementation_plan>`
6. Append this plan to the implementation details section of the task/subtask
7. Display a confirmation card indicating the implementation plan was successfully created

The implementation plan should be detailed and actionable, containing specific steps such as searching for files, creating new files, modifying existing files, etc. The goal is to frontload planning work into the task/subtask so execution can begin immediately.

Reference the existing 'update-subtask' command implementation as a starting point, as it uses a similar approach for appending content to tasks. Ensure proper error handling for cases where the specified ID doesn't exist or when API calls fail.

# Test Strategy:
Testing should verify:

1. Command correctly identifies and retrieves content for both task and subtask IDs
2. Implementation plans are properly generated and formatted with XML tags and timestamps
3. Plans are correctly appended to the implementation details section without overwriting existing content
4. The '--research' flag successfully switches the backend from Claude to Perplexity
5. Appropriate error messages are displayed for invalid IDs or API failures
6. Confirmation card is displayed after successful plan creation

Test cases should include:
- Running 'plan --id 123' on an existing task
- Running 'plan --id 123.1' on an existing subtask
- Running 'plan --id 123 --research' to test the Perplexity integration
- Running 'plan --id 999' with a non-existent ID to verify error handling
- Running the command on tasks with existing implementation plans to ensure proper appending

Manually review the quality of generated plans to ensure they provide actionable, step-by-step guidance that accurately reflects the task requirements.

# Subtasks:
## 1. Retrieve Task Content [in-progress]
### Dependencies: None
### Description: Fetch the content of the specified task from the task management system. This includes the task title, description, and any associated details.
### Details:
Implement a function to retrieve task details based on a task ID. Handle cases where the task does not exist.

## 2. Generate Implementation Plan with AI [pending]
### Dependencies: 40.1
### Description: Use an AI model (Claude or Perplexity) to generate an implementation plan based on the retrieved task content. The plan should outline the steps required to complete the task.
### Details:
Implement logic to switch between Claude and Perplexity APIs. Handle API authentication and rate limiting. Prompt the AI model with the task content and request a detailed implementation plan.

## 3. Format Plan in XML [pending]
### Dependencies: 40.2, 40.2
### Description: Format the generated implementation plan within XML tags. Each step in the plan should be represented as an XML element with appropriate attributes.
### Details:
Define the XML schema for the implementation plan. Implement a function to convert the AI-generated plan into the defined XML format. Ensure proper XML syntax and validation.

## 4. Error Handling and Output [pending]
### Dependencies: 40.3
### Description: Implement error handling for all steps, including API failures and XML formatting errors. Output the formatted XML plan to the console or a file.
### Details:
Add try-except blocks to handle potential exceptions. Log errors for debugging. Provide informative error messages to the user. Output the XML plan in a user-friendly format.

