# Task ID: 39
# Title: Update Project Licensing to Dual License Structure
# Status: done
# Dependencies: None
# Priority: high
# Description: Replace the current MIT license with a dual license structure that protects commercial rights for project owners while allowing non-commercial use under an open source license.
# Details:
This task requires implementing a comprehensive licensing update across the project:

1. Remove all instances of the MIT license from the codebase, including any MIT license files, headers in source files, and references in documentation.

2. Create a dual license structure with:
   - Business Source License (BSL) 1.1 or similar for commercial use, explicitly stating that commercial rights are exclusively reserved for Ralph & Eyal
   - Apache 2.0 for non-commercial use, allowing the community to use, modify, and distribute the code for non-commercial purposes

3. Update the license field in package.json to reflect the dual license structure (e.g., "BSL 1.1 / Apache 2.0")

4. Add a clear, concise explanation of the licensing terms in the README.md, including:
   - A summary of what users can and cannot do with the code
   - Who holds commercial rights
   - How to obtain commercial use permission if needed
   - Links to the full license texts

5. Create a detailed LICENSE.md file that includes:
   - Full text of both licenses
   - Clear delineation between commercial and non-commercial use
   - Specific definitions of what constitutes commercial use
   - Any additional terms or clarifications specific to this project

6. Create a CONTRIBUTING.md file that explicitly states:
   - Contributors must agree that their contributions will be subject to the project's dual licensing
   - Commercial rights for all contributions are assigned to Ralph & Eyal
   - Guidelines for acceptable contributions

7. Ensure all source code files include appropriate license headers that reference the dual license structure.

# Test Strategy:
To verify correct implementation, perform the following checks:

1. File verification:
   - Confirm the MIT license file has been removed
   - Verify LICENSE.md exists and contains both BSL and Apache 2.0 license texts
   - Confirm README.md includes the license section with clear explanation
   - Verify CONTRIBUTING.md exists with proper contributor guidelines
   - Check package.json for updated license field

2. Content verification:
   - Review LICENSE.md to ensure it properly describes the dual license structure with clear terms
   - Verify README.md license section is concise yet complete
   - Check that commercial rights are explicitly reserved for Ralph & Eyal in all relevant documents
   - Ensure CONTRIBUTING.md clearly explains the licensing implications for contributors

3. Legal review:
   - Have a team member not involved in the implementation review all license documents
   - Verify that the chosen BSL terms properly protect commercial interests
   - Confirm the Apache 2.0 implementation is correct and compatible with the BSL portions

4. Source code check:
   - Sample at least 10 source files to ensure they have updated license headers
   - Verify no MIT license references remain in any source files

5. Documentation check:
   - Ensure any documentation that mentioned licensing has been updated to reflect the new structure

# Subtasks:
## 1. Remove MIT License and Create Dual License Files [done]
### Dependencies: None
### Description: Remove all MIT license references from the codebase and create the new license files for the dual license structure.
### Details:
Implementation steps:
1. Scan the entire codebase to identify all instances of MIT license references (license files, headers in source files, documentation mentions).
2. Remove the MIT license file and all direct references to it.
3. Create a LICENSE.md file containing:
   - Full text of Business Source License (BSL) 1.1 with explicit commercial rights reservation for Ralph & Eyal
   - Full text of Apache 2.0 license for non-commercial use
   - Clear definitions of what constitutes commercial vs. non-commercial use
   - Specific terms for obtaining commercial use permission
4. Create a CONTRIBUTING.md file that explicitly states the contribution terms:
   - Contributors must agree to the dual licensing structure
   - Commercial rights for all contributions are assigned to Ralph & Eyal
   - Guidelines for acceptable contributions

Testing approach:
- Verify all MIT license references have been removed using a grep or similar search tool
- Have legal review of the LICENSE.md and CONTRIBUTING.md files to ensure they properly protect commercial rights
- Validate that the license files are properly formatted and readable

## 2. Update Source Code License Headers and Package Metadata [done]
### Dependencies: 39.1
### Description: Add appropriate dual license headers to all source code files and update package metadata to reflect the new licensing structure.
### Details:
Implementation steps:
1. Create a template for the new license header that references the dual license structure (BSL 1.1 / Apache 2.0).
2. Systematically update all source code files to include the new license header, replacing any existing MIT headers.
3. Update the license field in package.json to "BSL 1.1 / Apache 2.0".
4. Update any other metadata files (composer.json, setup.py, etc.) that contain license information.
5. Verify that any build scripts or tools that reference licensing information are updated.

Testing approach:
- Write a script to verify that all source files contain the new license header
- Validate package.json and other metadata files have the correct license field
- Ensure any build processes that depend on license information still function correctly
- Run a sample build to confirm license information is properly included in any generated artifacts

## 3. Update Documentation and Create License Explanation [done]
### Dependencies: 39.1, 39.2
### Description: Update project documentation to clearly explain the dual license structure and create comprehensive licensing guidance.
### Details:
Implementation steps:
1. Update the README.md with a clear, concise explanation of the licensing terms:
   - Summary of what users can and cannot do with the code
   - Who holds commercial rights (Ralph & Eyal)
   - How to obtain commercial use permission
   - Links to the full license texts
2. Create a dedicated LICENSING.md or similar document with detailed explanations of:
   - The rationale behind the dual licensing approach
   - Detailed examples of what constitutes commercial vs. non-commercial use
   - FAQs addressing common licensing questions
3. Update any other documentation references to licensing throughout the project.
4. Create visual aids (if appropriate) to help users understand the licensing structure.
5. Ensure all documentation links to licensing information are updated.

Testing approach:
- Have non-technical stakeholders review the documentation for clarity and understanding
- Verify all links to license files work correctly
- Ensure the explanation is comprehensive but concise enough for users to understand quickly
- Check that the documentation correctly addresses the most common use cases and questions

