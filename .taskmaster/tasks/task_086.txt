# Task ID: 86
# Title: Update .taskmasterconfig schema and user guide
# Status: pending
# Dependencies: 83
# Priority: medium
# Description: Create a migration guide for users to update their .taskmasterconfig files and document the new token limit configuration options.
# Details:
1. Create a migration script or guide for users to update their existing `.taskmasterconfig` files:

```javascript
// Example migration snippet for .taskmasterconfig
{
  "main": {
    // Before:
    // "maxTokens": 16000,
    
    // After:
    "maxInputTokens": 16000,
    "maxOutputTokens": 4000,
    "temperature": 0.7
  },
  "research": {
    "maxInputTokens": 16000,
    "maxOutputTokens": 4000,
    "temperature": 0.7
  },
  "fallback": {
    "maxInputTokens": 8000,
    "maxOutputTokens": 2000,
    "temperature": 0.7
  }
}
```

2. Update the user documentation to explain the new token limit fields:

```markdown
# Token Limit Configuration

Task Master now provides more granular control over token limits with separate settings for input and output tokens:

- `maxInputTokens`: Maximum number of tokens allowed in the input prompt (system prompt + user prompt)
- `maxOutputTokens`: Maximum number of tokens the model should generate in its response

## Benefits

- More precise control over token usage
- Better cost management
- Reduced likelihood of hitting model context limits
- Dynamic adjustment to maximize output space based on input length

## Migration from Previous Versions

If you're upgrading from a previous version, you'll need to update your `.taskmasterconfig` file:

1. Replace the single `maxTokens` field with separate `maxInputTokens` and `maxOutputTokens` fields
2. Recommended starting values:
   - Set `maxInputTokens` to your previous `maxTokens` value
   - Set `maxOutputTokens` to approximately 1/4 of your model's context window

## Example Configuration

```json
{
  "main": {
    "maxInputTokens": 16000,
    "maxOutputTokens": 4000,
    "temperature": 0.7
  }
}
```
```

3. Update the schema validation in `config-manager.js` to validate the new fields:

```javascript
function _validateConfig(config) {
  // ... existing validation
  
  // Validate token limits for each role
  ['main', 'research', 'fallback'].forEach(role => {
    if (config[role]) {
      // Check if old maxTokens is present and warn about migration
      if (config[role].maxTokens !== undefined) {
        console.warn(`Warning: 'maxTokens' in ${role} role is deprecated. Please use 'maxInputTokens' and 'maxOutputTokens' instead.`);
      }
      
      // Validate new token limit fields
      if (config[role].maxInputTokens !== undefined && (!Number.isInteger(config[role].maxInputTokens) || config[role].maxInputTokens <= 0)) {
        throw new Error(`Invalid maxInputTokens for ${role} role: must be a positive integer`);
      }
      
      if (config[role].maxOutputTokens !== undefined && (!Number.isInteger(config[role].maxOutputTokens) || config[role].maxOutputTokens <= 0)) {
        throw new Error(`Invalid maxOutputTokens for ${role} role: must be a positive integer`);
      }
    }
  });
  
  return config;
}
```

# Test Strategy:
1. Verify documentation is clear and provides migration steps
2. Test the validation logic with various config formats
3. Test backward compatibility with old config format
4. Ensure error messages are helpful when validation fails
