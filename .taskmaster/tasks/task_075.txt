# Task ID: 75
# Title: Integrate Google Search Grounding for Research Role
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Update the AI service layer to enable Google Search Grounding specifically when a Google model is used in the 'research' role.
# Details:
**Goal:** Conditionally enable Google Search Grounding based on the AI role.\n\n**Implementation Plan:**\n\n1.  **Modify `ai-services-unified.js`:** Update `generateTextService`, `streamTextService`, and `generateObjectService`.\n2.  **Conditional Logic:** Inside these functions, check if `providerName === 'google'` AND `role === 'research'`.\n3.  **Construct `providerOptions`:** If the condition is met, create an options object:\n    ```javascript\n    let providerSpecificOptions = {};\n    if (providerName === 'google' && role === 'research') {\n        log('info', 'Enabling Google Search Grounding for research role.');\n        providerSpecificOptions = {\n            google: {\n                useSearchGrounding: true,\n                // Optional: Add dynamic retrieval for compatible models\n                // dynamicRetrievalConfig: { mode: 'MODE_DYNAMIC' } \n            }\n        };\n    }\n    ```\n4.  **Pass Options to SDK:** Pass `providerSpecificOptions` to the Vercel AI SDK functions (`generateText`, `streamText`, `generateObject`) via the `providerOptions` parameter:\n    ```javascript\n    const { text, ... } = await generateText({\n        // ... other params\n        providerOptions: providerSpecificOptions \n    });\n    ```\n5.  **Update `supported-models.json`:** Ensure Google models intended for research (e.g., `gemini-1.5-pro-latest`, `gemini-1.5-flash-latest`) include `'research'` in their `allowed_roles` array.\n\n**Rationale:** This approach maintains the clear separation between 'main' and 'research' roles, ensuring grounding is only activated when explicitly requested via the `--research` flag or when the research model is invoked.\n\n**Clarification:** The Search Grounding feature is specifically designed to provide up-to-date information from the web when using Google models. This implementation ensures that grounding is only activated in research contexts where current information is needed, while preserving normal operation for standard tasks. The `useSearchGrounding: true` flag instructs the Google API to augment the model's knowledge with recent web search results relevant to the query.

# Test Strategy:
1. Configure a Google model (e.g., gemini-1.5-flash-latest) as the 'research' model in `.taskmasterconfig`.\n2. Run a command with the `--research` flag (e.g., `task-master add-task --prompt='Latest news on AI SDK 4.2' --research`).\n3. Verify logs show 'Enabling Google Search Grounding'.\n4. Check if the task output incorporates recent information.\n5. Configure the same Google model as the 'main' model.\n6. Run a command *without* the `--research` flag.\n7. Verify logs *do not* show grounding being enabled.\n8. Add unit tests to `ai-services-unified.test.js` to verify the conditional logic for adding `providerOptions`. Ensure mocks correctly simulate different roles and providers.

# Subtasks:
## 1. Modify AI service layer to support Google Search Grounding [pending]
### Dependencies: None
### Description: Update the AI service layer to include the capability to integrate with Google Search Grounding API for research-related queries.
### Details:
Extend the existing AI service layer by adding new methods and interfaces to handle Google Search Grounding API calls. This includes creating authentication mechanisms, request formatters, and response parsers specific to the Google Search API. Ensure proper error handling and retry logic for API failures.

## 2. Implement conditional logic for research role detection [pending]
### Dependencies: 75.1
### Description: Create logic to detect when a conversation is in 'research mode' and should trigger the Google Search Grounding functionality.
### Details:
Develop heuristics or machine learning-based detection to identify when a user's query requires research capabilities. Implement a decision tree that determines when to activate Google Search Grounding based on conversation context, explicit user requests for research, or specific keywords. Include configuration options to adjust sensitivity of the detection mechanism.

## 3. Update supported models configuration [pending]
### Dependencies: 75.1
### Description: Modify the model configuration to specify which AI models can utilize the Google Search Grounding capability.
### Details:
Update the model configuration files to include flags for Google Search Grounding compatibility. Create a registry of supported models with their specific parameters for optimal integration with the search API. Implement version checking to ensure compatibility between model versions and the Google Search Grounding API version.

## 4. Create end-to-end testing suite for research functionality [pending]
### Dependencies: 75.1, 75.2, 75.3
### Description: Develop comprehensive tests to verify the correct operation of the Google Search Grounding integration in research contexts.
### Details:
Build automated test cases that cover various research scenarios, including edge cases. Create mock responses for the Google Search API to enable testing without actual API calls. Implement integration tests that verify the entire flow from user query to research-enhanced response. Include performance benchmarks to ensure the integration doesn't significantly impact response times.

